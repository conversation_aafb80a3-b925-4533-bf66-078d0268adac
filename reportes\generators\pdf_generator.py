"""
reportes/generators/pdf_generator.py
Generador de reportes en formato PDF usando ReportLab.

Incluye funciones para generar reportes por empleado, área, ciudadano
y reportes generales con formato responsivo y profesional.
"""

from django.http import HttpResponse
from django.utils import timezone
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4, landscape
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus.flowables import KeepTogether
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from io import BytesIO
from datetime import datetime

# from ..utils import ReporteFormatHelper

class ReporteFormatHelper:
    """Clase auxiliar temporal para formateo."""

    @staticmethod
    def format_fecha(fecha):
        if hasattr(fecha, 'strftime'):
            return fecha.strftime('%d/%m/%Y %H:%M')
        return str(fecha)

    @staticmethod
    def get_estado_display(estado):
        estados = {1: 'Abierto', 2: 'En Progreso', 3: 'Cerrado', 4: 'Pendiente'}
        return estados.get(estado, 'Desconocido')

    @staticmethod
    def get_prioridad_display(prioridad):
        prioridades = {'baja': 'Baja', 'media': 'Media', 'alta': 'Alta', 'critica': 'Crítica'}
        return prioridades.get(prioridad, 'Desconocida')

    @staticmethod
    def truncate_text(text, max_length=50):
        if len(text) <= max_length:
            return text
        return text[:max_length-3] + '...'


class PDFReportGenerator:
    """
    Generador de reportes en PDF con formato profesional.
    """
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Configura estilos personalizados para el PDF."""
        # Estilo para el encabezado principal
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=18,
            spaceAfter=6,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#1A237E')
        ))
        
        # Estilo para el eslogan
        self.styles.add(ParagraphStyle(
            name='Slogan',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#666666'),
            fontName='Helvetica-Oblique'
        ))
        
        # Estilo para títulos de sección
        self.styles.add(ParagraphStyle(
            name='SectionTitle',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            spaceBefore=20,
            textColor=colors.HexColor('#1A237E'),
            borderWidth=1,
            borderColor=colors.HexColor('#1A237E'),
            borderPadding=5
        ))
        
        # Estilo para subtítulos
        self.styles.add(ParagraphStyle(
            name='SubTitle',
            parent=self.styles['Heading3'],
            fontSize=12,
            spaceAfter=8,
            spaceBefore=15,
            textColor=colors.HexColor('#283593')
        ))
    
    def _create_header(self, tipo_reporte, entidad_nombre=None, fecha_inicio=None, fecha_fin=None):
        """Crea el encabezado estándar del reporte."""
        story = []
        
        # Título principal
        story.append(Paragraph("Municipalidad de Estanzuela", self.styles['CustomTitle']))
        story.append(Paragraph("Un Gobierno de puertas abiertas", self.styles['Slogan']))
        
        # Tipo de reporte
        titulo_reporte = f"Reporte {tipo_reporte}"
        if entidad_nombre:
            titulo_reporte += f" - {entidad_nombre}"
        
        story.append(Paragraph(titulo_reporte, self.styles['SectionTitle']))
        
        # Rango de fechas
        if fecha_inicio or fecha_fin:
            if fecha_inicio and fecha_fin:
                rango = f"Período: {fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}"
            elif fecha_inicio:
                rango = f"Desde: {fecha_inicio.strftime('%d/%m/%Y')}"
            elif fecha_fin:
                rango = f"Hasta: {fecha_fin.strftime('%d/%m/%Y')}"
        else:
            rango = "Período: Todo el tiempo"
        
        story.append(Paragraph(rango, self.styles['Normal']))
        story.append(Spacer(1, 20))
        
        return story
    
    def _create_summary_table(self, resumen):
        """Crea la tabla de resumen por estados."""
        data = [
            ['Estado', 'Cantidad'],
            ['Total', str(resumen['total'])],
            ['Abiertos', str(resumen['abiertos'])],
            ['En Progreso', str(resumen['en_progreso'])],
            ['Cerrados', str(resumen['cerrados'])],
            ['Pendientes', str(resumen['pendientes'])]
        ]
        
        table = Table(data, colWidths=[3*inch, 1.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#1A237E')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 1), (0, -1), 'Helvetica-Bold'),  # Primera columna en negrita
        ]))
        
        return table
    
    def _create_tickets_table(self, tickets, estado_nombre):
        """Crea una tabla de tickets para un estado específico."""
        if not tickets.exists():
            return None
        
        # Encabezados de la tabla
        data = [['ID', 'Título', 'Prioridad', 'Área', 'Fecha Creación', 'Creado Por']]
        
        # Datos de los tickets
        for ticket in tickets[:50]:  # Limitar a 50 tickets por estado
            titulo = ReporteFormatHelper.truncate_text(ticket.titulo, 30)
            prioridad = ReporteFormatHelper.get_prioridad_display(ticket.prioridad)
            area = ticket.grupo.name if ticket.grupo else 'Sin área'
            fecha = ReporteFormatHelper.format_fecha(ticket.fecha_creacion)
            creador = ticket.creado_por.get_full_name() or ticket.creado_por.username
            
            data.append([
                str(ticket.id),
                titulo,
                prioridad,
                area,
                fecha,
                creador
            ])
        
        # Crear tabla
        table = Table(data, colWidths=[0.8*inch, 2.2*inch, 1*inch, 1.5*inch, 1.2*inch, 1.3*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#283593')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        return table
    
    def generar_reporte_empleado(self, datos_reporte, fecha_inicio=None, fecha_fin=None):
        """Genera reporte PDF por empleado."""
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)
        
        story = []
        
        for i, datos in enumerate(datos_reporte):
            empleado = datos['empleado']
            resumen = datos['resumen']
            tickets_por_estado = datos['tickets_por_estado']
            
            # Encabezado (solo en la primera página)
            if i == 0:
                story.extend(self._create_header(
                    "por Empleado",
                    empleado.get_full_name() or empleado.username,
                    fecha_inicio,
                    fecha_fin
                ))
            else:
                # Para empleados adicionales, agregar separador
                story.append(PageBreak())
                story.append(Paragraph(
                    f"Empleado: {empleado.get_full_name() or empleado.username}",
                    self.styles['SectionTitle']
                ))
                story.append(Spacer(1, 10))
            
            # Información del empleado
            info_empleado = f"<b>Usuario:</b> {empleado.username}<br/>"
            if empleado.cargo:
                info_empleado += f"<b>Cargo:</b> {empleado.cargo.nombre}<br/>"
            
            areas = [g.name for g in empleado.groups.all() if g.name in ['Administración', 'Secretaría', 'Fontanería', 'Electricidad', 'Mantenimiento', 'Limpieza', 'Jardinería', 'Seguridad', 'Obras Públicas', 'Servicios Generales']]
            if areas:
                info_empleado += f"<b>Áreas:</b> {', '.join(areas)}"
            
            story.append(Paragraph(info_empleado, self.styles['Normal']))
            story.append(Spacer(1, 15))
            
            # Tabla de resumen
            story.append(Paragraph("Resumen de Tickets", self.styles['SubTitle']))
            story.append(self._create_summary_table(resumen))
            story.append(Spacer(1, 20))
            
            # Tablas por estado
            estados = [
                (1, 'Abiertos'),
                (2, 'En Progreso'),
                (3, 'Cerrados'),
                (4, 'Pendientes')
            ]
            
            for estado_id, estado_nombre in estados:
                tickets = tickets_por_estado[estado_id]
                if tickets.exists():
                    story.append(Paragraph(f"Tickets {estado_nombre}", self.styles['SubTitle']))
                    tabla = self._create_tickets_table(tickets, estado_nombre)
                    if tabla:
                        story.append(tabla)
                        story.append(Spacer(1, 15))
        
        # Generar PDF
        doc.build(story)
        buffer.seek(0)
        
        # Crear respuesta HTTP
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        filename = f"reporte_empleado_{timezone.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response

    def generar_reporte_area(self, datos_reporte, fecha_inicio=None, fecha_fin=None):
        """Genera reporte PDF por área."""
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)

        story = []

        for i, datos in enumerate(datos_reporte):
            area = datos['area']
            resumen = datos['resumen']
            tickets_por_estado = datos['tickets_por_estado']

            # Encabezado (solo en la primera página)
            if i == 0:
                story.extend(self._create_header(
                    "por Área",
                    area.name,
                    fecha_inicio,
                    fecha_fin
                ))
            else:
                # Para áreas adicionales, agregar separador
                story.append(PageBreak())
                story.append(Paragraph(f"Área: {area.name}", self.styles['SectionTitle']))
                story.append(Spacer(1, 10))

            # Tabla de resumen
            story.append(Paragraph("Resumen de Tickets", self.styles['SubTitle']))
            story.append(self._create_summary_table(resumen))
            story.append(Spacer(1, 20))

            # Tablas por estado
            estados = [
                (1, 'Abiertos'),
                (2, 'En Progreso'),
                (3, 'Cerrados'),
                (4, 'Pendientes')
            ]

            for estado_id, estado_nombre in estados:
                tickets = tickets_por_estado[estado_id]
                if tickets.exists():
                    story.append(Paragraph(f"Tickets {estado_nombre}", self.styles['SubTitle']))
                    tabla = self._create_tickets_table(tickets, estado_nombre)
                    if tabla:
                        story.append(tabla)
                        story.append(Spacer(1, 15))

        # Generar PDF
        doc.build(story)
        buffer.seek(0)

        # Crear respuesta HTTP
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        filename = f"reporte_area_{timezone.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    def generar_reporte_ciudadano(self, datos_reporte, fecha_inicio=None, fecha_fin=None):
        """Genera reporte PDF por ciudadano."""
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)

        story = []

        for i, datos in enumerate(datos_reporte):
            ciudadano = datos['ciudadano']
            resumen = datos['resumen']
            tickets_por_estado = datos['tickets_por_estado']

            # Encabezado (solo en la primera página)
            if i == 0:
                story.extend(self._create_header(
                    "por Ciudadano",
                    ciudadano.nombre_completo,
                    fecha_inicio,
                    fecha_fin
                ))
            else:
                # Para ciudadanos adicionales, agregar separador
                story.append(PageBreak())
                story.append(Paragraph(f"Ciudadano: {ciudadano.nombre_completo}", self.styles['SectionTitle']))
                story.append(Spacer(1, 10))

            # Información del ciudadano
            info_ciudadano = f"<b>DPI:</b> {ciudadano.dpi}<br/>"
            info_ciudadano += f"<b>Teléfono:</b> {ciudadano.telefono}<br/>"
            if ciudadano.direccion:
                info_ciudadano += f"<b>Dirección:</b> {ciudadano.direccion}"

            story.append(Paragraph(info_ciudadano, self.styles['Normal']))
            story.append(Spacer(1, 15))

            # Tabla de resumen
            story.append(Paragraph("Resumen de Tickets", self.styles['SubTitle']))
            story.append(self._create_summary_table(resumen))
            story.append(Spacer(1, 20))

            # Tablas por estado
            estados = [
                (1, 'Abiertos'),
                (2, 'En Progreso'),
                (3, 'Cerrados'),
                (4, 'Pendientes')
            ]

            for estado_id, estado_nombre in estados:
                tickets = tickets_por_estado[estado_id]
                if tickets.exists():
                    story.append(Paragraph(f"Tickets {estado_nombre}", self.styles['SubTitle']))
                    tabla = self._create_tickets_table(tickets, estado_nombre)
                    if tabla:
                        story.append(tabla)
                        story.append(Spacer(1, 15))

        # Generar PDF
        doc.build(story)
        buffer.seek(0)

        # Crear respuesta HTTP
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        filename = f"reporte_ciudadano_{timezone.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    def _create_general_tickets_table(self, tickets):
        """Crea una tabla extendida para el reporte general (formato horizontal)."""
        if not tickets.exists():
            return None

        # Encabezados de la tabla (más columnas para formato horizontal)
        data = [['ID', 'Título', 'Descripción', 'Prioridad', 'Estado', 'Área', 'Asignado a', 'Ciudadano', 'Fecha Creación']]

        # Datos de los tickets
        for ticket in tickets[:100]:  # Limitar a 100 tickets
            titulo = ReporteFormatHelper.truncate_text(ticket.titulo, 25)
            descripcion = ReporteFormatHelper.truncate_text(ticket.descripcion, 30)
            prioridad = ReporteFormatHelper.get_prioridad_display(ticket.prioridad)
            estado = ReporteFormatHelper.get_estado_display(ticket.estado)
            area = ticket.grupo.name if ticket.grupo else 'Sin área'

            # Obtener asignado
            asignacion = ticket.asignaciones.filter(estado__in=[1, 2]).first()
            asignado = asignacion.usuario.get_full_name() if asignacion else 'Sin asignar'

            # Obtener ciudadano
            ciudadano_ticket = ticket.ciudadanos.first()
            ciudadano = ciudadano_ticket.ciudadano.nombre_completo if ciudadano_ticket else 'Sin ciudadano'

            fecha = ticket.fecha_creacion.strftime('%d/%m/%Y')

            data.append([
                str(ticket.id),
                titulo,
                descripcion,
                prioridad,
                estado,
                area,
                ReporteFormatHelper.truncate_text(asignado, 15),
                ReporteFormatHelper.truncate_text(ciudadano, 20),
                fecha
            ])

        # Crear tabla con anchos ajustados para formato horizontal
        table = Table(data, colWidths=[0.6*inch, 1.8*inch, 2*inch, 0.8*inch, 0.9*inch, 1.2*inch, 1.2*inch, 1.5*inch, 0.8*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#283593')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 8),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('FONTSIZE', (0, 1), (-1, -1), 7),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        return table

    def generar_reporte_general(self, datos_reporte, fecha_inicio=None, fecha_fin=None):
        """Genera reporte PDF general (formato horizontal)."""
        buffer = BytesIO()
        # Usar formato horizontal para más espacio
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4), rightMargin=36, leftMargin=36,
                              topMargin=72, bottomMargin=18)

        story = []

        resumen = datos_reporte['resumen']
        tickets_por_estado = datos_reporte['tickets_por_estado']

        # Encabezado
        story.extend(self._create_header(
            "General",
            None,
            fecha_inicio,
            fecha_fin
        ))

        # Tabla de resumen
        story.append(Paragraph("Resumen General de Tickets", self.styles['SubTitle']))
        story.append(self._create_summary_table(resumen))
        story.append(Spacer(1, 20))

        # Tablas por estado con formato extendido
        estados = [
            (1, 'Abiertos'),
            (2, 'En Progreso'),
            (3, 'Cerrados'),
            (4, 'Pendientes')
        ]

        for estado_id, estado_nombre in estados:
            tickets = tickets_por_estado[estado_id]
            if tickets.exists():
                story.append(Paragraph(f"Tickets {estado_nombre}", self.styles['SubTitle']))
                tabla = self._create_general_tickets_table(tickets)
                if tabla:
                    story.append(tabla)
                    story.append(Spacer(1, 15))

                    # Si hay muchos tickets, agregar salto de página
                    if tickets.count() > 20:
                        story.append(PageBreak())

        # Generar PDF
        doc.build(story)
        buffer.seek(0)

        # Crear respuesta HTTP
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        filename = f"reporte_general_{timezone.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response
