{% extends 'reportes/base_reportes.html' %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page">Por <PERSON></li>
{% endblock %}

{% block page_title %}Reportes por Ciudadano{% endblock %}
{% block page_description %}Genere reportes detallados de tickets por ciudadano{% endblock %}

{% block report_content %}
<div class="row">
    <div class="col-12">
        <form class="report-form" action="{% url 'reportes:generar_reporte_ciudadano' %}" method="post">
            {% csrf_token %}
            
            <!-- Selección de Ciudadanos -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-users me-2"></i>Selección de Ciudadanos
                </h5>
                
                <div class="row">
                    <div class="col-12">
                        <label for="ciudadanos" class="form-label">Ciudadanos *</label>
                        <select name="ciudadanos" id="ciudadanos" class="form-select select2" multiple required>
                            {% for ciudadano in ciudadanos %}
                            <option value="{{ ciudadano.id }}" 
                                    data-dpi="{{ ciudadano.dpi }}"
                                    data-telefono="{{ ciudadano.telefono }}">
                                {{ ciudadano.nombre_completo }} - {{ ciudadano.dpi }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            Puede seleccionar múltiples ciudadanos. Use el buscador para encontrar ciudadanos específicos.
                        </div>
                    </div>
                </div>
                
                <!-- Búsqueda avanzada -->
                <div class="row mt-3">
                    <div class="col-md-4">
                        <label for="buscar_nombre" class="form-label">Buscar por Nombre</label>
                        <input type="text" id="buscar_nombre" class="form-control" 
                               placeholder="Nombre completo...">
                    </div>
                    <div class="col-md-4">
                        <label for="buscar_dpi" class="form-label">Buscar por DPI</label>
                        <input type="text" id="buscar_dpi" class="form-control" 
                               placeholder="Número de DPI...">
                    </div>
                    <div class="col-md-4">
                        <label for="buscar_telefono" class="form-label">Buscar por Teléfono</label>
                        <input type="text" id="buscar_telefono" class="form-control" 
                               placeholder="Número de teléfono...">
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <label class="form-label">Acciones</label>
                        <div>
                            <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="buscarCiudadanos()">
                                <i class="fas fa-search me-1"></i>Buscar
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="limpiarBusqueda()">
                                <i class="fas fa-eraser me-1"></i>Limpiar Búsqueda
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="limpiarSeleccionCiudadanos()">
                                <i class="fas fa-times me-1"></i>Limpiar Selección
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filtros de Fecha -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-calendar-alt me-2"></i>Filtros de Fecha
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <label for="fecha_inicio" class="form-label">Fecha de Inicio</label>
                        <input type="date" name="fecha_inicio" id="fecha_inicio" class="form-control">
                        <div class="form-text">Opcional. Deje vacío para incluir desde el inicio.</div>
                    </div>
                    <div class="col-md-6">
                        <label for="fecha_fin" class="form-label">Fecha de Fin</label>
                        <input type="date" name="fecha_fin" id="fecha_fin" class="form-control">
                        <div class="form-text">Opcional. Deje vacío para incluir hasta la fecha actual.</div>
                    </div>
                </div>
            </div>
            
            <!-- Formato del Reporte -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-file-export me-2"></i>Formato del Reporte
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="formato" id="formato_pdf" value="pdf" checked>
                            <label class="form-check-label" for="formato_pdf">
                                <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                            </label>
                        </div>
                        <div class="form-text">Formato ideal para visualización e impresión.</div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="formato" id="formato_excel" value="excel">
                            <label class="form-check-label" for="formato_excel">
                                <i class="fas fa-file-excel text-success me-2"></i>Excel
                            </label>
                        </div>
                        <div class="form-text">Formato ideal para análisis de datos.</div>
                    </div>
                </div>
            </div>
            
            <!-- Botones de Acción -->
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-generate me-3">
                    <i class="fas fa-download me-2"></i>Generar Reporte
                </button>
                <a href="{% url 'reportes:index' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Volver
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Información sobre el reporte -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Información del Reporte por Ciudadano</h6>
            <p class="mb-2"><strong>Contenido del reporte:</strong></p>
            <ul class="mb-0">
                <li>Información básica del ciudadano (nombre, DPI, teléfono, dirección)</li>
                <li>Tabla de resumen con conteo de tickets por estado</li>
                <li>Listado detallado de tickets organizados por estado</li>
                <li>Si selecciona múltiples ciudadanos, cada uno tendrá su propia sección</li>
                <li>Incluye información del área asignada y empleado responsable</li>
            </ul>
        </div>
    </div>
</div>

<!-- Ayuda de búsqueda -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-warning">
            <h6><i class="fas fa-lightbulb me-2"></i>Consejos de Búsqueda</h6>
            <ul class="mb-0">
                <li>Use la búsqueda por nombre para encontrar ciudadanos por su nombre completo</li>
                <li>La búsqueda por DPI es útil cuando conoce el número exacto</li>
                <li>Puede buscar por teléfono si tiene esa información</li>
                <li>Los campos de búsqueda funcionan de forma independiente</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_report_js %}
<script>
$(document).ready(function() {
    // Configurar Select2
    $('#ciudadanos').select2({
        theme: 'bootstrap-5',
        placeholder: 'Seleccione ciudadanos...',
        allowClear: true,
        templateResult: formatCiudadano,
        templateSelection: formatCiudadanoSelection
    });
});

function formatCiudadano(ciudadano) {
    if (!ciudadano.id) {
        return ciudadano.text;
    }
    
    const $ciudadano = $(ciudadano.element);
    const dpi = $ciudadano.data('dpi');
    const telefono = $ciudadano.data('telefono');
    
    let html = '<div>';
    html += '<strong>' + ciudadano.text.split(' - ')[0] + '</strong>';
    html += '<br><small class="text-muted">DPI: ' + dpi + '</small>';
    if (telefono) {
        html += '<br><small class="text-info">Tel: ' + telefono + '</small>';
    }
    html += '</div>';
    
    return $(html);
}

function formatCiudadanoSelection(ciudadano) {
    return ciudadano.text.split(' - ')[0]; // Solo mostrar el nombre
}

function buscarCiudadanos() {
    const nombre = $('#buscar_nombre').val().toLowerCase();
    const dpi = $('#buscar_dpi').val();
    const telefono = $('#buscar_telefono').val();
    
    const select = $('#ciudadanos');
    
    select.find('option').each(function() {
        const $option = $(this);
        const text = $option.text().toLowerCase();
        const optionDpi = $option.data('dpi') || '';
        const optionTelefono = $option.data('telefono') || '';
        
        let visible = true;
        
        if (nombre && !text.includes(nombre)) {
            visible = false;
        }
        
        if (dpi && !optionDpi.includes(dpi)) {
            visible = false;
        }
        
        if (telefono && !optionTelefono.includes(telefono)) {
            visible = false;
        }
        
        $option.toggle(visible);
    });
    
    select.trigger('change.select2');
}

function limpiarBusqueda() {
    $('#buscar_nombre, #buscar_dpi, #buscar_telefono').val('');
    $('#ciudadanos option').show();
    $('#ciudadanos').trigger('change.select2');
}

function limpiarSeleccionCiudadanos() {
    $('#ciudadanos').val(null).trigger('change');
}
</script>
{% endblock %}
