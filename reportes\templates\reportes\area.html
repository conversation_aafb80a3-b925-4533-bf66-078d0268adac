{% extends 'reportes/base_reportes.html' %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active" aria-current="page">Por <PERSON></li>
{% endblock %}

{% block page_title %}Reportes por Área{% endblock %}
{% block page_description %}Genere reportes detallados de tickets por área o departamento{% endblock %}

{% block report_content %}
<div class="row">
    <div class="col-12">
        <form class="report-form" action="{% url 'reportes:generar_reporte_area' %}" method="post">
            {% csrf_token %}
            
            <!-- Selección de Áreas -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-building me-2"></i>Selección de Áreas
                </h5>
                
                <div class="row">
                    <div class="col-12">
                        <label for="areas" class="form-label"><PERSON><PERSON><PERSON> *</label>
                        <select name="areas" id="areas" class="form-select select2" multiple required>
                            {% for area in areas %}
                            <option value="{{ area.id }}">
                                {{ area.name }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            Puede seleccionar múltiples áreas. Use el buscador para encontrar áreas específicas.
                        </div>
                    </div>
                </div>
                
                <!-- Búsqueda y acciones -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <label for="buscar_area" class="form-label">Búsqueda Rápida</label>
                        <input type="text" id="buscar_area" class="form-control" 
                               placeholder="Buscar área...">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Acciones</label>
                        <div>
                            <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="seleccionarTodasAreas()">
                                <i class="fas fa-check-all me-1"></i>Seleccionar Todas
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="limpiarSeleccionAreas()">
                                <i class="fas fa-times me-1"></i>Limpiar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filtros de Fecha -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-calendar-alt me-2"></i>Filtros de Fecha
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <label for="fecha_inicio" class="form-label">Fecha de Inicio</label>
                        <input type="date" name="fecha_inicio" id="fecha_inicio" class="form-control">
                        <div class="form-text">Opcional. Deje vacío para incluir desde el inicio.</div>
                    </div>
                    <div class="col-md-6">
                        <label for="fecha_fin" class="form-label">Fecha de Fin</label>
                        <input type="date" name="fecha_fin" id="fecha_fin" class="form-control">
                        <div class="form-text">Opcional. Deje vacío para incluir hasta la fecha actual.</div>
                    </div>
                </div>
            </div>
            
            <!-- Formato del Reporte -->
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-file-export me-2"></i>Formato del Reporte
                </h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="formato" id="formato_pdf" value="pdf" checked>
                            <label class="form-check-label" for="formato_pdf">
                                <i class="fas fa-file-pdf text-danger me-2"></i>PDF
                            </label>
                        </div>
                        <div class="form-text">Formato ideal para visualización e impresión.</div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="formato" id="formato_excel" value="excel">
                            <label class="form-check-label" for="formato_excel">
                                <i class="fas fa-file-excel text-success me-2"></i>Excel
                            </label>
                        </div>
                        <div class="form-text">Formato ideal para análisis de datos.</div>
                    </div>
                </div>
            </div>
            
            <!-- Botones de Acción -->
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-generate me-3">
                    <i class="fas fa-download me-2"></i>Generar Reporte
                </button>
                <a href="{% url 'reportes:index' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Volver
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Lista de áreas disponibles -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>Áreas Disponibles
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for area in areas %}
                    <div class="col-md-4 col-sm-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-building text-primary me-2"></i>
                            <span>{{ area.name }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Información sobre el reporte -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>Información del Reporte por Área</h6>
            <p class="mb-2"><strong>Contenido del reporte:</strong></p>
            <ul class="mb-0">
                <li>Información del área o departamento</li>
                <li>Tabla de resumen con conteo de tickets por estado</li>
                <li>Listado detallado de tickets organizados por estado</li>
                <li>Si selecciona múltiples áreas, cada una tendrá su propia sección</li>
                <li>Incluye información del empleado asignado y ciudadano solicitante</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_report_js %}
<script>
$(document).ready(function() {
    // Configurar Select2
    $('#areas').select2({
        theme: 'bootstrap-5',
        placeholder: 'Seleccione áreas...',
        allowClear: true
    });
    
    // Búsqueda dinámica
    $('#buscar_area').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        const select = $('#areas');
        
        select.find('option').each(function() {
            const text = $(this).text().toLowerCase();
            const visible = text.includes(searchTerm);
            $(this).toggle(visible);
        });
        
        select.trigger('change.select2');
    });
});

function seleccionarTodasAreas() {
    $('#areas option').prop('selected', true);
    $('#areas').trigger('change');
}

function limpiarSeleccionAreas() {
    $('#areas').val(null).trigger('change');
}
</script>
{% endblock %}
