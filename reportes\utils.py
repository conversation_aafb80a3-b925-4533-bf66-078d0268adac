"""
reportes/utils.py
Utilidades para la generación de reportes.

Contiene funciones auxiliares para consultas de datos,
formateo y procesamiento de información para reportes.
"""

from django.db.models import Q, Count, Case, When, IntegerField
from django.contrib.auth.models import Group
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, date
from tickets.models import Ticket
from asignaciones.models import AsignacionTicket
from ciudadano.models import Ciudadano, CiudadanoTicket

User = get_user_model()


class ReporteDataHelper:
    """
    Clase auxiliar para obtener y procesar datos para reportes.
    """
    
    @staticmethod
    def get_tickets_por_empleado(empleado_id, fecha_inicio=None, fecha_fin=None):
        """
        Obtiene todos los tickets asignados a un empleado específico.
        
        Args:
            empleado_id: ID del empleado
            fecha_inicio: Fecha de inicio del filtro (opcional)
            fecha_fin: Fecha de fin del filtro (opcional)
            
        Returns:
            QuerySet de tickets filtrados
        """
        queryset = Ticket.objects.filter(
            asignaciones__usuario_id=empleado_id,
            is_active=True
        ).distinct()
        
        if fecha_inicio:
            queryset = queryset.filter(fecha_creacion__gte=fecha_inicio)
        if fecha_fin:
            queryset = queryset.filter(fecha_creacion__lte=fecha_fin)
            
        return queryset.select_related('creado_por', 'grupo').prefetch_related('asignaciones', 'ciudadanos')
    
    @staticmethod
    def get_tickets_por_area(area_id, fecha_inicio=None, fecha_fin=None):
        """
        Obtiene todos los tickets asignados a un área específica.
        
        Args:
            area_id: ID del área (grupo)
            fecha_inicio: Fecha de inicio del filtro (opcional)
            fecha_fin: Fecha de fin del filtro (opcional)
            
        Returns:
            QuerySet de tickets filtrados
        """
        queryset = Ticket.objects.filter(
            grupo_id=area_id,
            is_active=True
        )
        
        if fecha_inicio:
            queryset = queryset.filter(fecha_creacion__gte=fecha_inicio)
        if fecha_fin:
            queryset = queryset.filter(fecha_creacion__lte=fecha_fin)
            
        return queryset.select_related('creado_por', 'grupo').prefetch_related('asignaciones', 'ciudadanos')
    
    @staticmethod
    def get_tickets_por_ciudadano(ciudadano_id, fecha_inicio=None, fecha_fin=None):
        """
        Obtiene todos los tickets de un ciudadano específico.
        
        Args:
            ciudadano_id: ID del ciudadano
            fecha_inicio: Fecha de inicio del filtro (opcional)
            fecha_fin: Fecha de fin del filtro (opcional)
            
        Returns:
            QuerySet de tickets filtrados
        """
        queryset = Ticket.objects.filter(
            ciudadanos__ciudadano_id=ciudadano_id,
            is_active=True
        ).distinct()
        
        if fecha_inicio:
            queryset = queryset.filter(fecha_creacion__gte=fecha_inicio)
        if fecha_fin:
            queryset = queryset.filter(fecha_creacion__lte=fecha_fin)
            
        return queryset.select_related('creado_por', 'grupo').prefetch_related('asignaciones', 'ciudadanos')
    
    @staticmethod
    def get_resumen_por_estado(tickets_queryset):
        """
        Genera un resumen de tickets agrupados por estado.
        
        Args:
            tickets_queryset: QuerySet de tickets
            
        Returns:
            dict: Diccionario con conteos por estado
        """
        resumen = tickets_queryset.aggregate(
            total=Count('id'),
            abiertos=Count(Case(When(estado=1, then=1), output_field=IntegerField())),
            en_progreso=Count(Case(When(estado=2, then=1), output_field=IntegerField())),
            cerrados=Count(Case(When(estado=3, then=1), output_field=IntegerField())),
            pendientes=Count(Case(When(estado=4, then=1), output_field=IntegerField()))
        )
        
        return {
            'total': resumen['total'],
            'abiertos': resumen['abiertos'],
            'en_progreso': resumen['en_progreso'],
            'cerrados': resumen['cerrados'],
            'pendientes': resumen['pendientes']
        }
    
    @staticmethod
    def get_tickets_por_estado(tickets_queryset, estado):
        """
        Filtra tickets por estado específico.
        
        Args:
            tickets_queryset: QuerySet de tickets
            estado: Estado a filtrar (1=Abierto, 2=En Progreso, 3=Cerrado, 4=Pendiente)
            
        Returns:
            QuerySet filtrado por estado
        """
        return tickets_queryset.filter(estado=estado).order_by('-fecha_creacion')
    
    @staticmethod
    def get_empleados_activos():
        """
        Obtiene todos los empleados activos del sistema.
        
        Returns:
            QuerySet de usuarios activos
        """
        return User.objects.filter(
            is_active=True
        ).select_related('cargo').prefetch_related('groups').order_by('first_name', 'last_name')
    
    @staticmethod
    def get_areas_activas():
        """
        Obtiene todas las áreas activas del sistema.
        
        Returns:
            QuerySet de grupos que representan áreas
        """
        # Obtener solo grupos que representan áreas (no roles)
        from permissions.core import PermissionHelper
        return Group.objects.filter(
            name__in=PermissionHelper.AREA_GROUPS
        ).order_by('name')
    
    @staticmethod
    def get_ciudadanos_activos():
        """
        Obtiene todos los ciudadanos activos del sistema.
        
        Returns:
            QuerySet de ciudadanos activos
        """
        return Ciudadano.objects.filter(
            is_active=True
        ).order_by('nombre_completo')
    
    @staticmethod
    def get_tickets_generales(fecha_inicio=None, fecha_fin=None):
        """
        Obtiene todos los tickets del sistema para reporte general.
        
        Args:
            fecha_inicio: Fecha de inicio del filtro (opcional)
            fecha_fin: Fecha de fin del filtro (opcional)
            
        Returns:
            QuerySet de todos los tickets filtrados
        """
        queryset = Ticket.objects.filter(is_active=True)
        
        if fecha_inicio:
            queryset = queryset.filter(fecha_creacion__gte=fecha_inicio)
        if fecha_fin:
            queryset = queryset.filter(fecha_creacion__lte=fecha_fin)
            
        return queryset.select_related('creado_por', 'grupo').prefetch_related('asignaciones', 'ciudadanos')


class ReporteFormatHelper:
    """
    Clase auxiliar para formateo de datos en reportes.
    """
    
    @staticmethod
    def format_fecha(fecha):
        """Formatea una fecha para mostrar en reportes."""
        if isinstance(fecha, datetime):
            return fecha.strftime('%d/%m/%Y %H:%M')
        elif isinstance(fecha, date):
            return fecha.strftime('%d/%m/%Y')
        return str(fecha)
    
    @staticmethod
    def get_estado_display(estado):
        """Obtiene la representación textual del estado."""
        estados = {
            1: 'Abierto',
            2: 'En Progreso', 
            3: 'Cerrado',
            4: 'Pendiente'
        }
        return estados.get(estado, 'Desconocido')
    
    @staticmethod
    def get_prioridad_display(prioridad):
        """Obtiene la representación textual de la prioridad."""
        prioridades = {
            'baja': 'Baja',
            'media': 'Media',
            'alta': 'Alta',
            'critica': 'Crítica'
        }
        return prioridades.get(prioridad, 'Desconocida')
    
    @staticmethod
    def truncate_text(text, max_length=50):
        """Trunca texto para tablas."""
        if len(text) <= max_length:
            return text
        return text[:max_length-3] + '...'
