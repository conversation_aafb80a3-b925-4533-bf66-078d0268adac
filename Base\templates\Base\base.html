{% load static %}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}Sistema de Tickets{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'img/logo.png' %}">
    <link rel="shortcut icon" type="image/png" href="{% static 'img/logo.png' %}">
    <link rel="apple-touch-icon" href="{% static 'img/logo.png' %}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        /* Variables CSS */
        :root {
            --primary-color: #1A237E;
            --secondary-color: #283593;
            --tertiary-color: #3F51B5;
            --gray-color: #9E9E9E;
            --white-color: #FFFFFF;
            --accent-orange: #FF9800;
            --accent-green: #4CAF50;
            --accent-yellow: #FFEB3B;
        }

        /* Estilos globales */
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--white-color);
            margin: 0;
            padding: 0;
        }

        /* Sidebar */
        #sidebar-wrapper {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            width: 280px;
            transition: all 0.3s ease;
            box-shadow: 4px 0 20px rgba(0,0,0,0.15);
            overflow: hidden;
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-content {
            width: 280px;
            min-height: 100vh;
            overflow-y: auto;
        }

        #sidebar-wrapper.collapsed {
            width: 0;
        }

        /* Sidebar Header */
        .sidebar-heading {
            background: rgba(0,0,0,0.2);
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-heading h3 {
            color: white;
            margin: 0;
            font-weight: 600;
        }

        /* Menu Items */
        .list-group-item {
            background: transparent !important;
            border: none !important;
            color: rgba(255,255,255,0.9) !important;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .list-group-item:hover {
            background: rgba(255,255,255,0.1) !important;
            color: white !important;
            transform: translateX(8px);
        }

        /* Dropdown menus */
        .dropdown-menu-custom {
            background: rgba(0,0,0,0.3);
            border: none;
            margin: 0;
            padding: 0;
        }

        .dropdown-item-custom {
            color: rgba(255,255,255,0.8) !important;
            padding: 0.75rem 2.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
        }

        .dropdown-item-custom:hover {
            background: rgba(255,255,255,0.1) !important;
            color: white !important;
        }

        /* NO flechas automáticas */
        .dropdown-toggle-custom::after {
            display: none !important;
        }

        /* Main content */
        .main-content {
            margin-left: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
            width: calc(100% - 280px);
        }

        .main-content.collapsed {
            margin-left: 0;
            width: 100%;
        }

        /* Navbar sin sticky */
        .navbar {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            width: 100%;
            padding: 0.5rem 1rem;
            background-color: var(--white-color);
            position: relative;
            z-index: 1020;
            margin: 0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            #sidebar-wrapper {
                width: 0;
                transform: translateX(-100%);
            }
            
            #sidebar-wrapper.active {
                width: 280px;
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                width: 100%;
            }
        }

        /* Iconos coloridos */
        .text-primary { color: #64B5F6 !important; }
        .text-info { color: #4FC3F7 !important; }
        .text-warning { color: #FFB74D !important; }
        .text-success { color: #81C784 !important; }
        .text-danger { color: #E57373 !important; }
        .text-secondary { color: #B0BEC5 !important; }

        /* Separador */
        .sidebar-divider {
            border-top: 1px solid rgba(255,255,255,0.2);
            margin: 1rem 1.5rem;
        }

        /* Estilos para mensajes de advertencia de seguridad */
        .alert-warning {
            border-left: 5px solid #ffc107;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            animation: pulse-warning 2s infinite;
        }

        .alert-danger {
            border-left: 5px solid #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            animation: pulse-danger 2s infinite;
        }

        @keyframes pulse-warning {
            0%, 100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4); }
            50% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
        }

        @keyframes pulse-danger {
            0%, 100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4); }
            50% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
        }

        /* Hacer los mensajes más prominentes */
        .alert {
            font-weight: 500;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert .fas {
            animation: bounce 1s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-5px); }
            60% { transform: translateY(-3px); }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="d-flex">
        <!-- Sidebar Dinámico con Permisos -->
        {% include 'Base/sidebar_unificado.html' %}



        <!-- Contenido principal -->
        <div class="main-content" id="page-content-wrapper">
            <!-- Barra superior -->
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid px-0">
                    <button class="btn btn-link" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <span class="navbar-brand m-0">
                        <i class="fas fa-user-circle me-2"></i>Bienvenido, <strong>{{ user.username }}</strong>
                    </span>
                </div>
            </nav>

            <!-- Mensajes del sistema -->
            {% if messages %}
                <div class="container-fluid px-3 pt-3">
                    {% for message in messages %}
                        <div class="alert alert-{% if message.tags == 'error' %}danger{% elif message.tags == 'warning' %}warning{% elif message.tags == 'success' %}success{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'warning' %}exclamation-circle{% elif message.tags == 'success' %}check-circle{% else %}info-circle{% endif %} me-2 fs-5"></i>
                                <div class="flex-grow-1">
                                    {{ message|safe }}
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Contenido dinámico SIN padding excesivo -->
            <div class="container-fluid" style="padding: 1rem;">
                {% block content %}
                {% endblock %}
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function () {
            // Toggle sidebar
            $('#sidebarToggle').on('click', function () {
                const sidebar = $('#sidebar-wrapper');
                const content = $('.main-content');

                sidebar.toggleClass('collapsed active');
                content.toggleClass('collapsed');
                $(this).toggleClass('active');
            });

            // Manejar desplegables del sidebar
            $('.dropdown-toggle-custom').on('click', function(e) {
                e.preventDefault();
                const target = $(this).attr('href');
                $(target).collapse('toggle');
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
