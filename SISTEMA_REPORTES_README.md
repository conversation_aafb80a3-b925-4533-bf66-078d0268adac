# Sistema de Reportes - Municipalidad de Estanzuela

## Descripción General

El Sistema de Reportes es un módulo completo que permite generar reportes detallados del sistema de tickets en formatos PDF y Excel. Está diseñado específicamente para administradores y supervisores de la Municipalidad de Estanzuela.

## Características Principales

### 🔐 Control de Acceso
- **Acceso restringido**: Solo administradores y supervisores pueden acceder
- **Integración con sistema de permisos**: Utiliza el sistema de permisos existente
- **Auditoría**: Registro completo de reportes generados

### 📊 Tipos de Reportes

#### 1. Reportes por Empleado
- Información detallada de tickets asignados a empleados específicos
- Selección múltiple de empleados
- Búsqueda dinámica por nombre o usuario
- Incluye información del cargo y áreas del empleado

#### 2. Reportes por Área
- Reportes de tickets por departamento o área
- Selección múltiple de áreas
- Información completa de tickets del área

#### 3. Reportes por Ciudadano
- Reportes de tickets solicitados por ciudadanos específicos
- Búsqueda avanzada por nombre, DPI o teléfono
- Información completa del ciudadano

#### 4. Reportes Generales
- Reporte completo del sistema
- Formato horizontal para mayor información
- Incluye todos los tickets organizados por estado

### 📄 Formatos Disponibles

#### PDF
- **Encabezado estándar**: "Municipalidad de Estanzuela - Un Gobierno de puertas abiertas"
- **Formato responsivo**: Tablas que se adaptan al contenido
- **Formato horizontal**: Para reportes generales con más información
- **Estructura organizada**: Resumen por estados + tablas detalladas

#### Excel
- **Hojas separadas**: Una hoja por entidad o estado
- **Formato profesional**: Estilos y colores corporativos
- **Fácil análisis**: Ideal para filtrado y análisis de datos
- **Información completa**: Todas las columnas disponibles

### 🔍 Filtros y Búsquedas

#### Filtros de Fecha
- **Rango personalizado**: Fecha de inicio y fin
- **Filtros rápidos**: Hoy, esta semana, este mes
- **Todo el tiempo**: Opción por defecto

#### Búsquedas Dinámicas
- **Empleados**: Por nombre, usuario, cargo o área
- **Áreas**: Por nombre del área
- **Ciudadanos**: Por nombre, DPI o teléfono

## Estructura del Reporte

### Encabezado Estándar
```
Municipalidad de Estanzuela
Un Gobierno de puertas abiertas

Reporte [Tipo] - [Entidad]
Período: [Rango de fechas]
```

### Contenido por Entidad
1. **Información de la entidad** (empleado, área o ciudadano)
2. **Tabla de resumen** con conteo por estados:
   - Total de tickets
   - Abiertos
   - En Progreso
   - Cerrados
   - Pendientes

3. **Tablas detalladas por estado**:
   - Tickets Abiertos
   - Tickets En Progreso
   - Tickets Cerrados
   - Tickets Pendientes

### Información de Tickets
- ID del ticket
- Título
- Descripción (en reportes generales)
- Prioridad
- Estado
- Área responsable
- Empleado asignado
- Ciudadano solicitante
- Fecha de creación

## Instalación y Configuración

### Dependencias Requeridas
```bash
pip install openpyxl==3.1.5
```

### Configuración en Django
1. Agregar `'reportes'` a `INSTALLED_APPS`
2. Incluir URLs: `path('reportes/', include('reportes.urls'))`
3. Ejecutar migraciones: `python manage.py migrate reportes`

### Permisos
El sistema utiliza el sistema de permisos existente:
- `PermissionHelper.is_admin(user)` - Administradores
- `PermissionHelper.is_supervisor(user)` - Supervisores

## Uso del Sistema

### Acceso
1. Iniciar sesión como administrador o supervisor
2. Navegar a "Reportes" en el sidebar
3. Seleccionar el tipo de reporte deseado

### Generación de Reportes
1. **Seleccionar entidades**: Empleados, áreas o ciudadanos
2. **Configurar filtros**: Rango de fechas (opcional)
3. **Elegir formato**: PDF o Excel
4. **Generar**: El archivo se descarga automáticamente

### Historial
- Consultar reportes generados anteriormente
- Ver detalles de cada reporte
- Información de usuario generador y fecha

## Estructura de Archivos

```
reportes/
├── __init__.py
├── admin.py
├── apps.py
├── models.py              # Modelo ReporteGenerado
├── views.py               # Vistas principales
├── urls.py                # URLs del módulo
├── utils.py               # Utilidades y helpers
├── generators/
│   ├── __init__.py
│   ├── pdf_generator.py   # Generador de PDFs
│   └── excel_generator.py # Generador de Excel
├── templates/reportes/
│   ├── base_reportes.html # Template base
│   ├── index.html         # Página principal
│   ├── empleado.html      # Reportes por empleado
│   ├── area.html          # Reportes por área
│   ├── ciudadano.html     # Reportes por ciudadano
│   ├── general.html       # Reportes generales
│   └── historial.html     # Historial de reportes
└── migrations/
    ├── __init__.py
    ├── 0001_initial.py
    └── 0002_initial.py
```

## Tecnologías Utilizadas

- **Django**: Framework web
- **ReportLab**: Generación de PDFs
- **OpenPyXL**: Generación de Excel
- **Select2**: Búsquedas dinámicas
- **SweetAlert2**: Alertas y notificaciones
- **Bootstrap 5**: Interfaz de usuario

## Seguridad

- **Autenticación requerida**: Solo usuarios autenticados
- **Control de permisos**: Verificación en cada vista
- **Validación de datos**: Validación completa de formularios
- **Auditoría**: Registro de todos los reportes generados
- **Protección CSRF**: Tokens en todos los formularios

## Mantenimiento

### Logs y Auditoría
- Todos los reportes generados se registran en `ReporteGenerado`
- Información del usuario, fecha y parámetros utilizados
- Historial completo disponible para administradores

### Optimización
- Límites en consultas para evitar sobrecarga
- Paginación en búsquedas dinámicas
- Índices en base de datos para mejor rendimiento

## Soporte

Para soporte técnico o modificaciones, contactar al equipo de desarrollo del sistema de tickets municipal.

---

**Municipalidad de Estanzuela**  
*Un Gobierno de puertas abiertas*
