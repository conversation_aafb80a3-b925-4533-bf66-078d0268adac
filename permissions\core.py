"""
permissions/core.py
Sistema centralizado de permisos - Lógica principal.

Contiene la clase PermissionHelper que centraliza toda la lógica de verificación
de roles y permisos del sistema, eliminando duplicaciones y conflictos.
"""

from django.contrib.auth.models import Group
from django.db.models import Q


class PermissionHelper:
    """
    Clase centralizada para manejo de permisos y roles en el sistema.
    
    Sistema de roles basado en:
    - Cargo: Define el puesto (Administrador, Secretaria, Supervisor, Empleado)
    - is_supervisor: Campo booleano que otorga privilegios de supervisor
    - Grupos: Tanto roles (Admin, Secretaria, Empleado) como áreas (Fontanería, etc.)
    """
    
    # Grupos que representan roles del sistema (no áreas)
    ROLE_GROUPS = ['Admin', 'Secretaria', 'Empleado']
    
    # Grupos que representan áreas/departamentos
    AREA_GROUPS = [
        'Administración', 'Secretaría', 'Fontanería', 'Electricidad', 
        'Mantenimiento', 'Limpieza', 'Jardinería', 'Seguridad', 
        'O<PERSON>s Pública<PERSON>', 'Servicios Generales'
    ]
    
    # ============================================================================
    # MÉTODOS DE VERIFICACIÓN DE ROLES
    # ============================================================================
    
    @staticmethod
    def is_admin(user):
        """
        Verifica si un usuario es Admin o Superadmin.
        
        Un usuario es admin si:
        1. Es superusuario (is_superuser=True)
        2. Tiene cargo 'Administrador'
        3. Pertenece al grupo 'Admin'

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si es Admin/Superadmin, False en caso contrario
        """
        if not user.is_authenticated:
            return False

        # Superusuarios son automáticamente admin (PRIORIDAD MÁXIMA)
        if user.is_superuser:
            return True

        # Verificar por cargo
        if hasattr(user, 'cargo') and user.cargo:
            if user.cargo.nombre.lower() in ['administrador', 'admin']:
                return True

        # Verificar por grupo de rol (no área)
        return user.groups.filter(name='Admin').exists()
    
    @staticmethod
    def is_supervisor(user):
        """
        Verifica si un usuario es Supervisor.
        
        Un usuario es supervisor si:
        1. Tiene is_supervisor=True (PRIORIDAD - independientemente del grupo)
        2. Tiene cargo 'Supervisor'
        
        Args:
            user: Usuario a verificar
            
        Returns:
            bool: True si es Supervisor, False en caso contrario
        """
        if not user.is_authenticated:
            return False
        
        # El campo is_supervisor tiene PRIORIDAD ABSOLUTA
        if hasattr(user, 'is_supervisor') and user.is_supervisor:
            return True
            
        # Verificar por cargo
        if hasattr(user, 'cargo') and user.cargo:
            if user.cargo.nombre.lower() == 'supervisor':
                return True
                
        return False
    
    @staticmethod
    def is_secretaria(user):
        """
        Verifica si un usuario es Secretaria.
        
        Un usuario es secretaria si:
        1. Tiene cargo 'Secretaria'
        2. Pertenece al grupo 'Secretaria'

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si es Secretaria, False en caso contrario
        """
        if not user.is_authenticated:
            return False

        # Verificar por cargo
        if hasattr(user, 'cargo') and user.cargo:
            if user.cargo.nombre.lower() == 'secretaria':
                return True

        # Verificar por grupo de rol
        return user.groups.filter(name='Secretaria').exists()
    
    @staticmethod
    def is_empleado(user):
        """
        Verifica si un usuario es Empleado.
        
        Un usuario es empleado si:
        1. Tiene cargo 'Empleado'
        2. Pertenece al grupo 'Empleado'
        3. No es admin, secretaria ni supervisor (por exclusión)
        
        Args:
            user: Usuario a verificar
            
        Returns:
            bool: True si es Empleado, False en caso contrario
        """
        if not user.is_authenticated:
            return False
            
        # Si es admin, secretaria o supervisor, NO es empleado
        if (PermissionHelper.is_admin(user) or 
            PermissionHelper.is_secretaria(user) or 
            PermissionHelper.is_supervisor(user)):
            return False
            
        # Verificar por cargo
        if hasattr(user, 'cargo') and user.cargo:
            if user.cargo.nombre.lower() == 'empleado':
                return True
                
        # Verificar por grupo de rol
        return user.groups.filter(name='Empleado').exists()
    
    @staticmethod
    def get_user_role(user):
        """
        Obtiene el rol principal del usuario.
        
        Args:
            user: Usuario a verificar
            
        Returns:
            str: Rol del usuario ('Admin', 'Supervisor', 'Secretaria', 'Empleado', 'Sin rol')
        """
        if not user.is_authenticated:
            return 'Sin autenticar'
            
        # Orden de prioridad: Admin > Supervisor > Secretaria > Empleado
        if PermissionHelper.is_admin(user):
            return 'Admin'
        elif PermissionHelper.is_supervisor(user):
            return 'Supervisor'
        elif PermissionHelper.is_secretaria(user):
            return 'Secretaria'
        elif PermissionHelper.is_empleado(user):
            return 'Empleado'
        else:
            return 'Sin rol'
    
    @staticmethod
    def get_user_areas(user):
        """
        Obtiene las áreas/departamentos a las que pertenece el usuario.
        
        Args:
            user: Usuario a verificar
            
        Returns:
            QuerySet: Grupos de área del usuario
        """
        if not user.is_authenticated:
            return user.groups.none()
            
        return user.groups.filter(name__in=PermissionHelper.AREA_GROUPS)
    
    @staticmethod
    def get_user_role_groups(user):
        """
        Obtiene los grupos de rol del usuario (no áreas).
        
        Args:
            user: Usuario a verificar
            
        Returns:
            QuerySet: Grupos de rol del usuario
        """
        if not user.is_authenticated:
            return user.groups.none()
            
        return user.groups.filter(name__in=PermissionHelper.ROLE_GROUPS)

    # ============================================================================
    # MÉTODOS DE PERMISOS ESPECÍFICOS PARA TICKETS
    # ============================================================================

    @staticmethod
    def can_create_tickets(user):
        """
        Verifica si un usuario puede crear tickets.

        Pueden crear tickets: Admin, Secretaria

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si puede crear tickets
        """
        if not user.is_authenticated:
            return False

        return (PermissionHelper.is_admin(user) or
                PermissionHelper.is_secretaria(user))

    @staticmethod
    def can_assign_tickets(user):
        """
        Verifica si un usuario puede asignar tickets.

        Pueden asignar tickets: Admin, Secretaria (solo a grupos), Supervisor (solo de su área)

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si puede asignar tickets
        """
        if not user.is_authenticated:
            return False

        return (PermissionHelper.is_admin(user) or
                PermissionHelper.is_secretaria(user) or
                PermissionHelper.is_supervisor(user))

    @staticmethod
    def can_view_all_tickets(user):
        """
        Verifica si un usuario puede ver todos los tickets.

        Pueden ver todos los tickets: Solo Admin

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si puede ver todos los tickets
        """
        if not user.is_authenticated:
            return False

        return PermissionHelper.is_admin(user)

    @staticmethod
    def can_change_ticket_status(user):
        """
        Verifica si un usuario puede cambiar el estado de tickets.

        Pueden cambiar estado: Admin, Supervisor, Empleado (solo asignados)

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si puede cambiar estado de tickets
        """
        if not user.is_authenticated:
            return False

        return (PermissionHelper.is_admin(user) or
                PermissionHelper.is_supervisor(user) or
                PermissionHelper.is_empleado(user))

    @staticmethod
    def can_delete_tickets(user):
        """
        Verifica si un usuario puede eliminar/desactivar tickets.

        Pueden eliminar tickets: Solo Admin

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si puede eliminar tickets
        """
        if not user.is_authenticated:
            return False

        return PermissionHelper.is_admin(user)

    # ============================================================================
    # MÉTODOS DE PERMISOS PARA GESTIÓN DE USUARIOS
    # ============================================================================

    @staticmethod
    def can_manage_users(user):
        """
        Verifica si un usuario puede gestionar otros usuarios.

        Pueden gestionar usuarios: Solo Admin

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si puede gestionar usuarios
        """
        if not user.is_authenticated:
            return False

        return PermissionHelper.is_admin(user)

    @staticmethod
    def can_view_user_details(user, target_user=None):
        """
        Verifica si un usuario puede ver detalles de otros usuarios.

        Args:
            user: Usuario que quiere ver
            target_user: Usuario objetivo (opcional)

        Returns:
            bool: True si puede ver detalles
        """
        if not user.is_authenticated:
            return False

        # Admin puede ver cualquier usuario
        if PermissionHelper.is_admin(user):
            return True

        # Supervisor puede ver usuarios de su área
        if PermissionHelper.is_supervisor(user) and target_user:
            user_areas = PermissionHelper.get_user_areas(user)
            target_areas = PermissionHelper.get_user_areas(target_user)
            return user_areas.intersection(target_areas).exists()

        # Usuarios pueden ver su propio perfil
        if target_user:
            return user == target_user

        return False

    # ============================================================================
    # MÉTODOS DE PERMISOS PARA NOTIFICACIONES
    # ============================================================================

    @staticmethod
    def can_create_notifications(user):
        """
        Verifica si un usuario puede crear notificaciones.

        Pueden crear notificaciones: Admin, Supervisor, Secretaria

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si puede crear notificaciones
        """
        if not user.is_authenticated:
            return False

        return (PermissionHelper.is_admin(user) or
                PermissionHelper.is_supervisor(user) or
                PermissionHelper.is_secretaria(user))

    @staticmethod
    def can_send_mass_notifications(user):
        """
        Verifica si un usuario puede enviar notificaciones masivas.

        Pueden enviar notificaciones masivas: Solo Admin

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si puede enviar notificaciones masivas
        """
        if not user.is_authenticated:
            return False

        return PermissionHelper.is_admin(user)

    @staticmethod
    def can_manage_all_notifications(user):
        """
        Verifica si un usuario puede gestionar todas las notificaciones.

        Pueden gestionar todas las notificaciones: Solo Admin

        Args:
            user: Usuario a verificar

        Returns:
            bool: True si puede gestionar todas las notificaciones
        """
        if not user.is_authenticated:
            return False

        return PermissionHelper.is_admin(user)

    # ============================================================================
    # MÉTODOS PARA SIDEBAR Y NAVEGACIÓN
    # ============================================================================

    @staticmethod
    def get_sidebar_permissions(user):
        """
        Obtiene los permisos específicos para mostrar elementos del sidebar.

        Args:
            user: Usuario a verificar

        Returns:
            dict: Diccionario con permisos para el sidebar
        """
        if not user.is_authenticated:
            return {
                'show_dashboard': False,
                'show_tickets': False,
                'show_create_ticket': False,
                'show_all_tickets': False,
                'show_my_tickets': False,
                'show_citizens': False,
                'show_users': False,
                'show_assignments': False,
                'show_notifications': False,
                'show_reports': False,
            }

        is_admin = PermissionHelper.is_admin(user)
        is_supervisor = PermissionHelper.is_supervisor(user)
        is_secretaria = PermissionHelper.is_secretaria(user)
        is_empleado = PermissionHelper.is_empleado(user)

        return {
            'show_dashboard': True,  # Todos ven dashboard
            'show_tickets': True,  # Todos ven algún tipo de tickets
            'show_create_ticket': is_admin or is_secretaria,
            'show_all_tickets': is_admin,
            'show_my_tickets': is_empleado or is_supervisor,
            'show_group_tickets': is_supervisor,
            'show_citizens': is_admin or is_secretaria or is_supervisor,
            'show_users': is_admin,
            'show_assignments': is_admin or is_supervisor,
            'show_notifications': True,  # Todos ven notificaciones
            'show_reports': is_admin or is_supervisor,
        }

    @staticmethod
    def get_quick_access_permissions(user):
        """
        Obtiene los permisos para mostrar accesos rápidos en el dashboard.

        Args:
            user: Usuario a verificar

        Returns:
            dict: Diccionario con permisos para accesos rápidos
        """
        if not user.is_authenticated:
            return {}

        is_admin = PermissionHelper.is_admin(user)
        is_supervisor = PermissionHelper.is_supervisor(user)
        is_secretaria = PermissionHelper.is_secretaria(user)
        is_empleado = PermissionHelper.is_empleado(user)

        return {
            # Accesos comunes
            'show_tickets_list': True,  # Todos pueden ver algún tipo de lista
            'show_my_notifications': True,  # Todos ven sus notificaciones

            # Admin
            'show_create_ticket': is_admin or is_secretaria,
            'show_manage_users': is_admin,
            'show_system_reports': is_admin,
            'show_all_assignments': is_admin,

            # Supervisor
            'show_group_management': is_supervisor,
            'show_group_reports': is_supervisor,
            'show_assign_tickets': is_supervisor,

            # Secretaria
            'show_citizen_management': is_admin or is_secretaria or is_supervisor,
            'show_register_citizen': is_admin or is_secretaria,

            # Empleado
            'show_my_assignments': is_empleado or is_supervisor,
            'show_change_status': is_empleado or is_supervisor or is_admin,
        }
