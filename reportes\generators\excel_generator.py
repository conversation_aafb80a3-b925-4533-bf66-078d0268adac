"""
reportes/generators/excel_generator.py
Generador de reportes en formato Excel usando openpyxl.

Incluye funciones para generar reportes por empleado, área, ciudadano
y reportes generales con hojas separadas y formato profesional.
"""

from django.http import HttpResponse
from django.utils import timezone
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
from io import BytesIO
from datetime import datetime

from ..utils import ReporteFormatHelper


class ExcelReportGenerator:
    """
    Generador de reportes en Excel con formato profesional.
    """
    
    def __init__(self):
        self.setup_styles()
    
    def setup_styles(self):
        """Configura los estilos para el Excel."""
        # Fuentes
        self.title_font = Font(name='Arial', size=16, bold=True, color='1A237E')
        self.header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
        self.subheader_font = Font(name='Arial', size=11, bold=True, color='283593')
        self.normal_font = Font(name='Arial', size=10)
        self.small_font = Font(name='Arial', size=9)
        
        # Rellenos
        self.header_fill = PatternFill(start_color='1A237E', end_color='1A237E', fill_type='solid')
        self.subheader_fill = PatternFill(start_color='E3F2FD', end_color='E3F2FD', fill_type='solid')
        self.summary_fill = PatternFill(start_color='F5F5F5', end_color='F5F5F5', fill_type='solid')
        
        # Bordes
        self.thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Alineaciones
        self.center_alignment = Alignment(horizontal='center', vertical='center')
        self.left_alignment = Alignment(horizontal='left', vertical='center')
    
    def _create_header(self, ws, tipo_reporte, entidad_nombre=None, fecha_inicio=None, fecha_fin=None):
        """Crea el encabezado estándar en la hoja."""
        # Título principal
        ws['A1'] = 'Municipalidad de Estanzuela'
        ws['A1'].font = self.title_font
        ws['A1'].alignment = self.center_alignment
        
        # Eslogan
        ws['A2'] = 'Un Gobierno de puertas abiertas'
        ws['A2'].font = Font(name='Arial', size=12, italic=True, color='666666')
        ws['A2'].alignment = self.center_alignment
        
        # Tipo de reporte
        titulo_reporte = f"Reporte {tipo_reporte}"
        if entidad_nombre:
            titulo_reporte += f" - {entidad_nombre}"
        
        ws['A4'] = titulo_reporte
        ws['A4'].font = self.subheader_font
        ws['A4'].alignment = self.center_alignment
        
        # Rango de fechas
        if fecha_inicio or fecha_fin:
            if fecha_inicio and fecha_fin:
                rango = f"Período: {fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}"
            elif fecha_inicio:
                rango = f"Desde: {fecha_inicio.strftime('%d/%m/%Y')}"
            elif fecha_fin:
                rango = f"Hasta: {fecha_fin.strftime('%d/%m/%Y')}"
        else:
            rango = "Período: Todo el tiempo"
        
        ws['A5'] = rango
        ws['A5'].font = self.normal_font
        ws['A5'].alignment = self.center_alignment
        
        return 7  # Fila donde continuar
    
    def _create_summary_section(self, ws, resumen, start_row):
        """Crea la sección de resumen."""
        # Título de la sección
        ws[f'A{start_row}'] = 'Resumen de Tickets'
        ws[f'A{start_row}'].font = self.subheader_font
        
        # Encabezados de la tabla de resumen
        headers = ['Estado', 'Cantidad']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row + 2, column=col, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.border = self.thin_border
            cell.alignment = self.center_alignment
        
        # Datos del resumen
        summary_data = [
            ('Total', resumen['total']),
            ('Abiertos', resumen['abiertos']),
            ('En Progreso', resumen['en_progreso']),
            ('Cerrados', resumen['cerrados']),
            ('Pendientes', resumen['pendientes'])
        ]
        
        for row_idx, (estado, cantidad) in enumerate(summary_data, start_row + 3):
            ws.cell(row=row_idx, column=1, value=estado).font = Font(name='Arial', size=10, bold=True)
            ws.cell(row=row_idx, column=1).border = self.thin_border
            ws.cell(row=row_idx, column=1).fill = self.summary_fill
            ws.cell(row=row_idx, column=1).alignment = self.left_alignment
            
            ws.cell(row=row_idx, column=2, value=cantidad).font = self.normal_font
            ws.cell(row=row_idx, column=2).border = self.thin_border
            ws.cell(row=row_idx, column=2).alignment = self.center_alignment
        
        # Ajustar ancho de columnas
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 12
        
        return start_row + len(summary_data) + 4  # Siguiente fila disponible
    
    def _create_tickets_section(self, ws, tickets, estado_nombre, start_row):
        """Crea una sección de tickets para un estado específico."""
        if not tickets.exists():
            return start_row
        
        # Título de la sección
        ws[f'A{start_row}'] = f'Tickets {estado_nombre}'
        ws[f'A{start_row}'].font = self.subheader_font
        
        # Encabezados de la tabla
        headers = ['ID', 'Título', 'Prioridad', 'Área', 'Fecha Creación', 'Creado Por']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row + 2, column=col, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.border = self.thin_border
            cell.alignment = self.center_alignment
        
        # Datos de los tickets
        current_row = start_row + 3
        for ticket in tickets[:100]:  # Limitar a 100 tickets
            data = [
                ticket.id,
                ticket.titulo,
                ReporteFormatHelper.get_prioridad_display(ticket.prioridad),
                ticket.grupo.name if ticket.grupo else 'Sin área',
                ReporteFormatHelper.format_fecha(ticket.fecha_creacion),
                ticket.creado_por.get_full_name() or ticket.creado_por.username
            ]
            
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=current_row, column=col, value=value)
                cell.font = self.small_font
                cell.border = self.thin_border
                cell.alignment = self.left_alignment if col in [2, 4, 6] else self.center_alignment
            
            current_row += 1
        
        # Ajustar ancho de columnas
        column_widths = [8, 40, 12, 20, 18, 25]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(col)].width = width
        
        return current_row + 2  # Siguiente fila disponible
    
    def generar_reporte_empleado(self, datos_reporte, fecha_inicio=None, fecha_fin=None):
        """Genera reporte Excel por empleado."""
        wb = Workbook()
        
        # Eliminar hoja por defecto
        wb.remove(wb.active)
        
        for datos in datos_reporte:
            empleado = datos['empleado']
            resumen = datos['resumen']
            tickets_por_estado = datos['tickets_por_estado']
            
            # Crear hoja para el empleado
            sheet_name = f"{empleado.get_full_name() or empleado.username}"[:31]  # Límite de Excel
            ws = wb.create_sheet(title=sheet_name)
            
            # Encabezado
            current_row = self._create_header(
                ws, "por Empleado", 
                empleado.get_full_name() or empleado.username,
                fecha_inicio, fecha_fin
            )
            
            # Información del empleado
            ws[f'A{current_row}'] = 'Información del Empleado'
            ws[f'A{current_row}'].font = self.subheader_font
            current_row += 1
            
            ws[f'A{current_row}'] = f'Usuario: {empleado.username}'
            current_row += 1
            
            if empleado.cargo:
                ws[f'A{current_row}'] = f'Cargo: {empleado.cargo.nombre}'
                current_row += 1
            
            areas = [g.name for g in empleado.groups.all() if g.name in ['Administración', 'Secretaría', 'Fontanería', 'Electricidad', 'Mantenimiento', 'Limpieza', 'Jardinería', 'Seguridad', 'Obras Públicas', 'Servicios Generales']]
            if areas:
                ws[f'A{current_row}'] = f'Áreas: {", ".join(areas)}'
                current_row += 1
            
            current_row += 2
            
            # Resumen
            current_row = self._create_summary_section(ws, resumen, current_row)
            
            # Secciones por estado
            estados = [
                (1, 'Abiertos'),
                (2, 'En Progreso'),
                (3, 'Cerrados'),
                (4, 'Pendientes')
            ]
            
            for estado_id, estado_nombre in estados:
                tickets = tickets_por_estado[estado_id]
                current_row = self._create_tickets_section(ws, tickets, estado_nombre, current_row)
        
        # Generar respuesta
        buffer = BytesIO()
        wb.save(buffer)
        buffer.seek(0)
        
        response = HttpResponse(
            buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f"reporte_empleado_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response

    def generar_reporte_area(self, datos_reporte, fecha_inicio=None, fecha_fin=None):
        """Genera reporte Excel por área."""
        wb = Workbook()

        # Eliminar hoja por defecto
        wb.remove(wb.active)

        for datos in datos_reporte:
            area = datos['area']
            resumen = datos['resumen']
            tickets_por_estado = datos['tickets_por_estado']

            # Crear hoja para el área
            sheet_name = area.name[:31]  # Límite de Excel
            ws = wb.create_sheet(title=sheet_name)

            # Encabezado
            current_row = self._create_header(
                ws, "por Área", area.name,
                fecha_inicio, fecha_fin
            )

            # Resumen
            current_row = self._create_summary_section(ws, resumen, current_row)

            # Secciones por estado
            estados = [
                (1, 'Abiertos'),
                (2, 'En Progreso'),
                (3, 'Cerrados'),
                (4, 'Pendientes')
            ]

            for estado_id, estado_nombre in estados:
                tickets = tickets_por_estado[estado_id]
                current_row = self._create_tickets_section(ws, tickets, estado_nombre, current_row)

        # Generar respuesta
        buffer = BytesIO()
        wb.save(buffer)
        buffer.seek(0)

        response = HttpResponse(
            buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f"reporte_area_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    def generar_reporte_ciudadano(self, datos_reporte, fecha_inicio=None, fecha_fin=None):
        """Genera reporte Excel por ciudadano."""
        wb = Workbook()

        # Eliminar hoja por defecto
        wb.remove(wb.active)

        for datos in datos_reporte:
            ciudadano = datos['ciudadano']
            resumen = datos['resumen']
            tickets_por_estado = datos['tickets_por_estado']

            # Crear hoja para el ciudadano
            sheet_name = ciudadano.nombre_completo[:31]  # Límite de Excel
            ws = wb.create_sheet(title=sheet_name)

            # Encabezado
            current_row = self._create_header(
                ws, "por Ciudadano", ciudadano.nombre_completo,
                fecha_inicio, fecha_fin
            )

            # Información del ciudadano
            ws[f'A{current_row}'] = 'Información del Ciudadano'
            ws[f'A{current_row}'].font = self.subheader_font
            current_row += 1

            ws[f'A{current_row}'] = f'DPI: {ciudadano.dpi}'
            current_row += 1

            ws[f'A{current_row}'] = f'Teléfono: {ciudadano.telefono}'
            current_row += 1

            if ciudadano.direccion:
                ws[f'A{current_row}'] = f'Dirección: {ciudadano.direccion}'
                current_row += 1

            current_row += 2

            # Resumen
            current_row = self._create_summary_section(ws, resumen, current_row)

            # Secciones por estado
            estados = [
                (1, 'Abiertos'),
                (2, 'En Progreso'),
                (3, 'Cerrados'),
                (4, 'Pendientes')
            ]

            for estado_id, estado_nombre in estados:
                tickets = tickets_por_estado[estado_id]
                current_row = self._create_tickets_section(ws, tickets, estado_nombre, current_row)

        # Generar respuesta
        buffer = BytesIO()
        wb.save(buffer)
        buffer.seek(0)

        response = HttpResponse(
            buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f"reporte_ciudadano_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

    def _create_general_tickets_section(self, ws, tickets, estado_nombre, start_row):
        """Crea una sección de tickets extendida para el reporte general."""
        if not tickets.exists():
            return start_row

        # Título de la sección
        ws[f'A{start_row}'] = f'Tickets {estado_nombre}'
        ws[f'A{start_row}'].font = self.subheader_font

        # Encabezados de la tabla (más columnas para reporte general)
        headers = ['ID', 'Título', 'Descripción', 'Prioridad', 'Estado', 'Área', 'Asignado a', 'Ciudadano', 'Fecha Creación']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=start_row + 2, column=col, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.border = self.thin_border
            cell.alignment = self.center_alignment

        # Datos de los tickets
        current_row = start_row + 3
        for ticket in tickets[:200]:  # Más tickets para reporte general
            # Obtener asignado
            asignacion = ticket.asignaciones.filter(estado__in=[1, 2]).first()
            asignado = asignacion.usuario.get_full_name() if asignacion else 'Sin asignar'

            # Obtener ciudadano
            ciudadano_ticket = ticket.ciudadanos.first()
            ciudadano = ciudadano_ticket.ciudadano.nombre_completo if ciudadano_ticket else 'Sin ciudadano'

            data = [
                ticket.id,
                ticket.titulo,
                ticket.descripcion,
                ReporteFormatHelper.get_prioridad_display(ticket.prioridad),
                ReporteFormatHelper.get_estado_display(ticket.estado),
                ticket.grupo.name if ticket.grupo else 'Sin área',
                asignado,
                ciudadano,
                ReporteFormatHelper.format_fecha(ticket.fecha_creacion)
            ]

            for col, value in enumerate(data, 1):
                cell = ws.cell(row=current_row, column=col, value=value)
                cell.font = self.small_font
                cell.border = self.thin_border
                cell.alignment = self.left_alignment if col in [2, 3, 7, 8] else self.center_alignment

            current_row += 1

        # Ajustar ancho de columnas para reporte general
        column_widths = [8, 30, 40, 12, 12, 18, 20, 25, 18]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(col)].width = width

        return current_row + 2  # Siguiente fila disponible

    def generar_reporte_general(self, datos_reporte, fecha_inicio=None, fecha_fin=None):
        """Genera reporte Excel general con hojas separadas por estado."""
        wb = Workbook()

        # Eliminar hoja por defecto
        wb.remove(wb.active)

        resumen = datos_reporte['resumen']
        tickets_por_estado = datos_reporte['tickets_por_estado']

        # Crear hoja de resumen
        ws_resumen = wb.create_sheet(title="Resumen General")

        # Encabezado en hoja de resumen
        current_row = self._create_header(ws_resumen, "General", None, fecha_inicio, fecha_fin)

        # Resumen general
        self._create_summary_section(ws_resumen, resumen, current_row)

        # Crear una hoja por cada estado
        estados = [
            (1, 'Abiertos'),
            (2, 'En Progreso'),
            (3, 'Cerrados'),
            (4, 'Pendientes')
        ]

        for estado_id, estado_nombre in estados:
            tickets = tickets_por_estado[estado_id]
            if tickets.exists():
                # Crear hoja para este estado
                ws_estado = wb.create_sheet(title=f"Tickets {estado_nombre}")

                # Encabezado
                current_row = self._create_header(
                    ws_estado, f"General - {estado_nombre}",
                    None, fecha_inicio, fecha_fin
                )

                # Tabla de tickets
                self._create_general_tickets_section(ws_estado, tickets, estado_nombre, current_row)

        # Generar respuesta
        buffer = BytesIO()
        wb.save(buffer)
        buffer.seek(0)

        response = HttpResponse(
            buffer.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f"reporte_general_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response
