<!-- Sidebar Unificado con <PERSON><PERSON><PERSON> -->
<div id="sidebar-wrapper">
    <div class="sidebar-content">
        <!-- Header del Sidebar -->
        <div class="sidebar-heading">
            <div class="d-flex align-items-center">
                <i class="fas fa-ticket-alt me-2 fs-4"></i>
                <div>
                    <h5 class="mb-0 text-white">Sistema de Tickets</h5>
                    <small class="text-white-50">
                        {{ user_permissions.role }}
                        {% if user_permissions.areas %}
                            - {{ user_permissions.areas|join:", " }}
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>

        <!-- Navegación Principal -->
        <div class="list-group list-group-flush">
            
            <!-- Dashboard - Todos los usuarios autenticados -->
            <a href="/inicio/" class="list-group-item d-flex align-items-center">
                <i class="fas fa-tachometer-alt me-3 text-primary"></i>
                <span>Inicio</span>
            </a>

            <!-- Tickets - Solo para Admin, Supervisor y Secretaria (NO empleados) -->
            {% if not user_permissions.is_empleado %}
            <a href="#ticketsSubmenu" class="list-group-item dropdown-toggle-custom collapsed d-flex align-items-center justify-content-between"
               data-bs-toggle="collapse" aria-expanded="false">
                <div class="d-flex align-items-center">
                    <i class="fas fa-ticket-alt me-3 text-info"></i>
                    <span>Tickets</span>
                </div>
                <i class="fas fa-chevron-down transition-icon"></i>
            </a>
            <div class="collapse dropdown-menu-custom" id="ticketsSubmenu">

                <!-- Ver todos los tickets - Solo Admin -->
                {% if user_permissions.can_view_all_tickets %}
                <a href="{% url 'tickets:lista_tickets' %}" class="dropdown-item-custom">
                    <i class="fas fa-list me-2"></i> Ver Todos los Tickets
                </a>
                {% endif %}

                <!-- Crear ticket - Admin y Secretaria -->
                {% if user_permissions.can_create_tickets %}
                <a href="{% url 'tickets:crear_ticket' %}" class="dropdown-item-custom">
                    <i class="fas fa-plus me-2"></i> Crear Nuevo Ticket
                </a>
                {% endif %}

                <!-- Si no es admin, mostrar opciones limitadas -->
                {% if not user_permissions.can_view_all_tickets %}
                    {% if user_permissions.is_supervisor %}
                    <a href="{% url 'tickets:lista_tickets' %}" class="dropdown-item-custom">
                        <i class="fas fa-eye me-2"></i> Tickets de Área
                    </a>
                    {% endif %}
                {% endif %}
            </div>
            {% endif %}

            <!-- Asignaciones - Admin y Supervisor -->
            {% if user_permissions.is_admin or user_permissions.is_supervisor %}
            <a href="#asignacionesSubmenu" class="list-group-item dropdown-toggle-custom collapsed d-flex align-items-center justify-content-between"
               data-bs-toggle="collapse" aria-expanded="false">
                <div class="d-flex align-items-center">
                    <i class="fas fa-tasks me-3 text-warning"></i>
                    <span>Asignaciones</span>
                </div>
                <i class="fas fa-chevron-down transition-icon"></i>
            </a>
            <div class="collapse dropdown-menu-custom" id="asignacionesSubmenu">
                <a href="{% url 'asignaciones:mis_asignaciones' %}" class="dropdown-item-custom">
                    <i class="fas fa-user-check me-2"></i> Mis Asignaciones
                </a>
                {% if user_permissions.is_admin or user_permissions.is_supervisor %}
                <a href="{% url 'asignaciones:lista_asignaciones' %}" class="dropdown-item-custom">
                    <i class="fas fa-users-cog me-2"></i> Gestionar Asignaciones
                </a>
                {% endif %}
            </div>
            {% endif %}

            <!-- Mis Asignaciones - Solo para Empleados -->
            {% if user_permissions.is_empleado %}
            <a href="{% url 'asignaciones:mis_asignaciones' %}" class="list-group-item d-flex align-items-center">
                <i class="fas fa-user-check me-3 text-warning"></i>
                <span>Mis Asignaciones</span>
            </a>
            {% endif %}

            <!-- Ciudadanos - Admin, Secretaria, Supervisor -->
            {% if user_permissions.is_admin or user_permissions.is_secretaria or user_permissions.is_supervisor %}
            <a href="#ciudadanosSubmenu" class="list-group-item dropdown-toggle-custom collapsed d-flex align-items-center justify-content-between" 
               data-bs-toggle="collapse" aria-expanded="false">
                <div class="d-flex align-items-center">
                    <i class="fas fa-users me-3 text-success"></i>
                    <span>Ciudadanos</span>
                </div>
                <i class="fas fa-chevron-down transition-icon"></i>
            </a>
            <div class="collapse dropdown-menu-custom" id="ciudadanosSubmenu">
                <a href="{% url 'ciudadano:lista_ciudadanos' %}" class="dropdown-item-custom">
                    <i class="fas fa-list me-2"></i> Lista de Ciudadanos
                </a>
                {% if user_permissions.is_admin or user_permissions.is_secretaria %}
                <a href="{% url 'ciudadano:crear_ciudadano' %}" class="dropdown-item-custom">
                    <i class="fas fa-user-plus me-2"></i> Registrar Ciudadano
                </a>
                <a href="{% url 'ciudadano:buscar_ciudadano' %}" class="dropdown-item-custom">
                    <i class="fas fa-search me-2"></i> Buscar Ciudadano
                </a>
                {% endif %}
            </div>
            {% endif %}

            <!-- Notificaciones - Todos -->
            <a href="#notificacionesSubmenu" class="list-group-item dropdown-toggle-custom collapsed d-flex align-items-center justify-content-between" 
               data-bs-toggle="collapse" aria-expanded="false">
                <div class="d-flex align-items-center">
                    <i class="fas fa-bell me-3 text-danger"></i>
                    <span>Notificaciones</span>
                </div>
                <i class="fas fa-chevron-down transition-icon"></i>
            </a>
            <div class="collapse dropdown-menu-custom" id="notificacionesSubmenu">
                <a href="{% url 'notificaciones:mis_notificaciones' %}" class="dropdown-item-custom">
                    <i class="fas fa-inbox me-2"></i> Mis Notificaciones
                </a>
                {% if user_permissions.can_create_notifications %}
                <div class="dropdown-divider"></div>
                <a href="{% url 'notificaciones:lista_notificaciones' %}" class="dropdown-item-custom">
                    <i class="fas fa-cogs me-2"></i> Gestionar Notificaciones
                </a>
                <a href="{% url 'notificaciones:crear_notificacion_usuario' %}" class="dropdown-item-custom">
                    <i class="fas fa-user-plus me-2"></i> Notificar Usuarios
                </a>
                <a href="{% url 'notificaciones:crear_notificacion_grupo' %}" class="dropdown-item-custom">
                    <i class="fas fa-users me-2"></i> Notificar Grupos
                </a>
                {% if user_permissions.can_send_mass_notifications %}
                <a href="{% url 'notificaciones:crear_notificacion_masiva' %}" class="dropdown-item-custom text-warning">
                    <i class="fas fa-broadcast-tower me-2"></i> Notificación Masiva
                </a>
                {% endif %}
                {% endif %}
            </div>

            <!-- Usuarios - Solo Admin -->
            {% if user_permissions.can_manage_users %}
            <a href="#usuariosSubmenu" class="list-group-item dropdown-toggle-custom collapsed d-flex align-items-center justify-content-between" 
               data-bs-toggle="collapse" aria-expanded="false">
                <div class="d-flex align-items-center">
                    <i class="fas fa-user-cog me-3 text-secondary"></i>
                    <span>Usuarios</span>
                </div>
                <i class="fas fa-chevron-down transition-icon"></i>
            </a>
            <div class="collapse dropdown-menu-custom" id="usuariosSubmenu">
                <a href="{% url 'user:lista_usuarios' %}" class="dropdown-item-custom">
                    <i class="fas fa-list me-2"></i> Lista de Usuarios
                </a>
                <a href="{% url 'user:crear_usuario_paso1' %}" class="dropdown-item-custom">
                    <i class="fas fa-user-plus me-2"></i> Crear Usuario
                </a>
            </div>
            {% endif %}

            <!-- Reportes - Solo Administradores y Superadministradores -->
            {% if user_permissions.is_admin %}
            <a href="#reportesSubmenu" class="list-group-item dropdown-toggle-custom collapsed d-flex align-items-center justify-content-between"
               data-bs-toggle="collapse" aria-expanded="false">
                <div class="d-flex align-items-center">
                    <i class="fas fa-chart-bar me-3 text-info"></i>
                    <span>Reportes</span>
                </div>
                <i class="fas fa-chevron-down transition-icon"></i>
            </a>
            <div class="collapse dropdown-menu-custom" id="reportesSubmenu">
                <a href="{% url 'reportes:index' %}" class="dropdown-item-custom">
                    <i class="fas fa-home me-2"></i> Panel de Reportes
                </a>
                <a href="{% url 'reportes:reporte_empleado' %}" class="dropdown-item-custom">
                    <i class="fas fa-user-tie me-2"></i> Por Empleado
                </a>
                <a href="{% url 'reportes:reporte_area' %}" class="dropdown-item-custom">
                    <i class="fas fa-building me-2"></i> Por Área
                </a>
                <a href="{% url 'reportes:reporte_ciudadano' %}" class="dropdown-item-custom">
                    <i class="fas fa-users me-2"></i> Por Ciudadano
                </a>
                <a href="{% url 'reportes:reporte_general' %}" class="dropdown-item-custom">
                    <i class="fas fa-chart-line me-2"></i> General
                </a>
                <a href="{% url 'reportes:historial' %}" class="dropdown-item-custom">
                    <i class="fas fa-history me-2"></i> Historial
                </a>
            </div>
            {% endif %}

            <!-- Seguridad - Solo Admin -->
            {% if user_permissions.is_admin %}
            <a href="#seguridadSubmenu" class="list-group-item dropdown-toggle-custom collapsed d-flex align-items-center justify-content-between"
               data-bs-toggle="collapse" aria-expanded="false">
                <div class="d-flex align-items-center">
                    <i class="fas fa-shield-alt me-3 text-danger"></i>
                    <span>Seguridad</span>
                </div>
                <i class="fas fa-chevron-down transition-icon"></i>
            </a>
            <div class="collapse dropdown-menu-custom" id="seguridadSubmenu">
                <a href="{% url 'base:security_stats' %}" class="dropdown-item-custom">
                    <i class="fas fa-chart-pie me-2"></i> Estadísticas de Seguridad
                </a>
                {% if user.is_superuser %}
                <a href="/admin/Base/unauthorizedaccessattempt/" class="dropdown-item-custom" target="_blank">
                    <i class="fas fa-cog me-2"></i> Administrar Intentos
                </a>
                {% endif %}
            </div>
            {% endif %}

            <!-- Separador -->
            <div class="sidebar-divider"></div>

            <!-- Cerrar Sesión -->
            <a href="{% url 'logout' %}" class="list-group-item text-danger d-flex align-items-center">
                <i class="fas fa-sign-out-alt me-3"></i>
                <span>Cerrar Sesión</span>
            </a>

        </div>

        <!-- Información del usuario al final -->
        <div class="sidebar-footer mt-auto p-3">
            <div class="text-center">
                <div class="user-info">
                    <i class="fas fa-user-circle fs-3 text-white-50"></i>
                    <div class="mt-2">
                        <small class="text-white-50 d-block">{{ user.get_full_name|default:user.username }}</small>
                        <small class="text-white-50">{{ user_permissions.role }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


